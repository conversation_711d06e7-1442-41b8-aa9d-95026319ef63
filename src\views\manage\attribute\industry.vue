<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="行业名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入行业名称" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增行业</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="行业名称" align="center" prop="name" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改行业对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="500px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="100px">
                <el-form-item label="行业名称" prop="name">
                    <el-input v-model="dataForm.name" placeholder="请输入行业名称" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getIndustryList, addIndustry, updateIndustry, getIndustry, delIndustry } from "@/api/manage/industry";
export default {
    name: "Industry",
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                name: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                name: ""
            },
            dataRules: {
                name: [
                    { required: true, message: '行业名称不能为空', trigger: 'blur' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    let dataForm = JSON.parse(JSON.stringify(this.dataForm));

                    if (this.isChange) {
                        dataForm.id = this.editId;
                        let res = await updateIndustry(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    } else {
                        let res = await addIndustry(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增行业";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改行业";
            let res = await getIndustry(val.id)
            if (res.code == 200) {
                this.dataForm = res.data;
                this.editId = val.id;
            }
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除该行业吗？')
                .then(async (_) => {
                    let res = await delIndustry(val.id)
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                name: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getIndustryList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        }
    },
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
